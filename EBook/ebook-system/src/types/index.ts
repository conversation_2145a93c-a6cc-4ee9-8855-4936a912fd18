// 学校接口
export interface School {
  id: number;
  schoolName: string;
  schoolCode?: string;
  location?: string;
  description?: string;
  createdBy?: number;
  updatedBy?: number;
  createTime: string;
  updateTime: string;
  collegeCount?: number;
  colleges?: College[];
}

// 学院接口 (匹配后端API)
export interface College {
  id: number;
  schoolId: number;
  collegeName: string;
  schoolName?: string;
  createdBy?: number;
  updatedBy?: number;
  createTime: string;
  updateTime: string;
  majorCount?: number;
  majors?: Major[];
}

// 专业接口 (匹配后端API)
export interface Major {
  id: number;
  collegeId: number;
  majorName: string;
  collegeName?: string;
  schoolName?: string;
  createTime: string;
  updateTime: string;
  courseCount?: number;
  courses?: Course[];
}

// 课程接口 (匹配后端API)
export interface Course {
  id: number;
  majorId?: number;
  courseName: string;
  referenceMaterials?: string;
  createTime: string;
  updateTime: string;
  majorName?: string;
  textbookCount?: number;
  textbooks?: Textbook[];
}

// 下载链接接口
export interface DownloadLink {
  id: string;
  type: 'zlib' | 'anna' | 'direct';
  name: string;
  url: string;
  description?: string;
}

// 教材接口 (匹配后端API)
export interface Textbook {
  id: number;
  textbookName: string;
  isbn?: string | null;
  zlibId?: string | null;
  annaArchiveId?: string | null;
  createTime: string;
  updateTime: string;
  materialType?: string; // TEXTBOOK-使用教材, REFERENCE-参考教材
  courseId?: number; // 关联的课程ID
  courseName?: string; // 关联的课程名称（后端返回）
  courseCount?: number;
  courseNames?: string[];
  downloadLinks?: DownloadLink[]; // 下载链接列表
  coverImageUrl?: string; // 封面图片URL
}

// API 响应接口 (匹配后端格式)
export interface ApiResponse<T> {
  success: boolean;
  message: string | null;
  data: T;
}

// 导航面包屑接口
export interface BreadcrumbItem {
  label: string;
  href?: string;
  isActive?: boolean;
}

// 搜索过滤器接口 (简化版)
export interface SearchFilters {
  keyword?: string;
  collegeId?: string;
  majorId?: string;
  courseId?: string;
}

// 分页接口
export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// 列表响应接口
export interface ListResponse<T> {
  items: T[];
  pagination: Pagination;
}

// 树形节点接口 (用于层级展示)
export interface TreeNode {
  id: string;
  label: string;
  type: 'college' | 'major' | 'course' | 'textbook';
  children?: TreeNode[];
  data?: College | Major | Course | Textbook;
}