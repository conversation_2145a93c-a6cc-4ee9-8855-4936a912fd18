'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Plus, Search, Edit, Trash2, Book, BookOpen, Download } from 'lucide-react'
import { Textbook, Course } from '@/types'
import { textbookApi, courseApi, adminApi } from '@/lib/api'
import { formatDate } from '@/lib/utils'
import EditTextbookForm from '@/components/admin/EditTextbookForm'

export default function TextbooksManagement() {
  const [textbooks, setTextbooks] = useState<Textbook[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedMaterialType, setSelectedMaterialType] = useState<string>('')
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingTextbook, setEditingTextbook] = useState<Textbook | null>(null)

  // 获取教材列表 - 使用管理员API
  const fetchTextbooks = async () => {
    try {
      setLoading(true)
      console.log('开始获取教材列表...')
      const response = await adminApi.textbooks.getList()
      console.log('教材列表响应:', response)

      if (response.data.success) {
        // 检查响应数据结构
        const textbooksData = response.data.data?.records || response.data.data || []
        console.log('教材数据:', textbooksData)
        setTextbooks(textbooksData)
      } else {
        console.error('获取教材列表失败:', response.data.message)
        setTextbooks([])
      }
    } catch (error: any) {
      console.error('获取教材列表失败:', error)
      console.error('错误详情:', {
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      })

      if (error.response?.status === 401) {
        alert('认证失败，请重新登录')
        window.location.href = '/admin/login'
      } else {
        alert('获取教材列表失败: ' + (error.response?.data?.message || error.message))
      }
      setTextbooks([])
    } finally {
      setLoading(false)
    }
  }



  useEffect(() => {
    fetchTextbooks()
  }, [])

  // 过滤教材列表
  const filteredTextbooks = textbooks.filter(textbook => {
    const matchesSearch = textbook.textbookName.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesMaterialType = !selectedMaterialType || textbook.materialType === selectedMaterialType
    
    return matchesSearch && matchesMaterialType
  })

  // 删除教材
  const handleDelete = async (id: number) => {
    if (confirm('确定要删除这本教材吗？此操作不可撤销。')) {
      try {
        const response = await adminApi.textbooks.delete(id)
        if (response.data.success) {
          // 成功删除后刷新列表
          fetchTextbooks()
        } else {
          alert(`删除失败: ${response.data.message}`)
        }
      } catch (error) {
        console.error('删除教材失败:', error)
        alert('删除教材时发生错误')
      }
    }
  }

  // 保存编辑后的教材信息
  const handleSaveTextbook = (updatedTextbook: Textbook) => {
    // 更新本地状态
    setTextbooks(prevTextbooks => 
      prevTextbooks.map(textbook => 
        textbook.id === updatedTextbook.id ? updatedTextbook : textbook
      )
    )
    setEditingTextbook(null)
    // 显示成功消息
    alert('教材信息更新成功')
  }

  // 处理搜索
  const handleSearch = async () => {
    if (searchTerm.trim()) {
      try {
        setLoading(true)
        console.log('搜索教材:', searchTerm)
        const response = await adminApi.textbooks.getList({ keyword: searchTerm })
        console.log('搜索响应:', response)

        if (response.data.success) {
          const textbooksData = response.data.data?.records || response.data.data || []
          setTextbooks(textbooksData)
        } else {
          console.error('搜索教材失败:', response.data.message)
        }
      } catch (error: any) {
        console.error('搜索教材失败:', error)
        if (error.response?.status === 401) {
          alert('认证失败，请重新登录')
          window.location.href = '/admin/login'
        }
      } finally {
        setLoading(false)
      }
    } else {
      fetchTextbooks()
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">加载中...</div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* 页面头部 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">教材管理</h1>
          <p className="text-gray-600 mt-1">管理系统中的所有教材资源</p>
        </div>
        <Button 
          onClick={() => setShowAddModal(true)}
          className="bg-blue-600 hover:bg-blue-700"
        >
          <Plus className="h-4 w-4 mr-2" />
          添加教材
        </Button>
      </div>

      {/* 搜索和筛选栏 */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative md:col-span-2">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="搜索教材名称..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            <select
              value={selectedMaterialType}
              onChange={(e) => setSelectedMaterialType(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">所有类型</option>
              <option value="TEXTBOOK">使用教材</option>
              <option value="REFERENCE">参考教材</option>
            </select>
          </div>
          <div className="mt-4 flex justify-end">
            <Button onClick={handleSearch} variant="outline">
              <Search className="h-4 w-4 mr-2" />
              搜索
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* 统计信息 */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">教材总数</CardTitle>
            <Book className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{textbooks.length}</div>
            <p className="text-xs text-gray-500">已收录教材</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">使用教材</CardTitle>
            <BookOpen className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {textbooks.filter(book => book.materialType === 'TEXTBOOK').length}
            </div>
            <p className="text-xs text-gray-500">主要使用教材</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">参考教材</CardTitle>
            <Book className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {textbooks.filter(book => book.materialType === 'REFERENCE').length}
            </div>
            <p className="text-xs text-gray-500">参考资料</p>
          </CardContent>
        </Card>
      </div>

      {/* 教材列表 */}
      <Card>
        <CardHeader>
          <CardTitle>教材列表</CardTitle>
          <CardDescription>
            共 {filteredTextbooks.length} 本教材
          </CardDescription>
        </CardHeader>
        <CardContent>
          {filteredTextbooks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              {searchTerm || selectedMaterialType ? '没有找到匹配的教材' : '暂无教材数据'}
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">教材信息</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">类型</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">关联课程</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">下载链接</th>
                    <th className="text-right py-3 px-4 font-medium text-gray-900">操作</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTextbooks.map((textbook) => (
                    <tr key={textbook.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div className="flex items-start space-x-3">
                          {textbook.coverImageUrl ? (
                            <img 
                              src={textbook.coverImageUrl} 
                              alt={textbook.textbookName}
                              className="w-12 h-16 object-cover rounded"
                            />
                          ) : (
                            <div className="w-12 h-16 bg-gray-200 rounded flex items-center justify-center">
                              <Book className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                          <div>
                            <div className="font-medium text-gray-900">{textbook.textbookName}</div>
                            <div className="text-sm text-gray-500">
                              创建时间: {formatDate(textbook.createTime)}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          textbook.materialType === 'TEXTBOOK' 
                            ? 'bg-blue-100 text-blue-800'
                            : textbook.materialType === 'REFERENCE'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {textbook.materialType === 'TEXTBOOK' ? '使用教材' : 
                           textbook.materialType === 'REFERENCE' ? '参考教材' : '未设置'}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600">
                        <div className="max-w-xs">
                          {textbook.courseName
                            ? textbook.courseName
                            : textbook.courseNames && textbook.courseNames.length > 0
                            ? textbook.courseNames.slice(0, 2).join(', ') +
                              (textbook.courseNames.length > 2 ? '...' : '')
                            : '未关联课程'
                          }
                          {textbook.courseCount && textbook.courseCount > 0 && (
                            <div className="text-xs text-gray-400">
                              共 {textbook.courseCount} 门课程
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        {textbook.downloadLinks && textbook.downloadLinks.length > 0 ? (
                          <div className="flex items-center space-x-1">
                            <Download className="h-4 w-4 text-green-600" />
                            <span className="text-sm text-green-600">
                              {textbook.downloadLinks.length} 个链接
                            </span>
                          </div>
                        ) : (
                          <span className="text-sm text-gray-400">无下载链接</span>
                        )}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex items-center justify-end space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => setEditingTextbook(textbook)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDelete(textbook.id)}
                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 添加教材模态框 - 占位 */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">添加教材</h3>
            <p className="text-gray-600 mb-4">添加教材功能正在开发中...</p>
            <div className="flex justify-end space-x-2">
              <Button
                variant="outline"
                onClick={() => setShowAddModal(false)}
              >
                取消
              </Button>
              <Button onClick={() => setShowAddModal(false)}>
                确定
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* 编辑教材模态框 */}
      {editingTextbook && (
        <EditTextbookForm
          textbook={editingTextbook}
          onSave={handleSaveTextbook}
          onCancel={() => setEditingTextbook(null)}
        />
      )}
    </div>
  )
}