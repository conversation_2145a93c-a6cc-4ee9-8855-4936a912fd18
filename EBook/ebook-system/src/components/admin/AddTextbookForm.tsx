'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { adminApi } from '@/lib/api'
import { Textbook } from '@/types'

interface AddTextbookFormProps {
  courseId: number
  onAdd: (textbook: Textbook) => void
  onCancel: () => void
}

export default function AddTextbookForm({ courseId, onAdd, onCancel }: AddTextbookFormProps) {
  const [formData, setFormData] = useState({
    textbookName: '',
    isbn: '',
    zlibId: '',
    annaArchiveId: '',
    materialType: 'TEXTBOOK'
  })
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const response = await adminApi.textbooks.create({
        ...formData,
        courseId: courseId
      })
      
      if (response.data.success) {
        onAdd(response.data.data)
      } else {
        setError(response.data.message || '添加失败')
      }
    } catch (err) {
      console.error('添加教材失败:', err)
      setError('添加教材时发生错误')
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-gray-500 bg-opacity-70 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md shadow-2xl">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">添加教材</h3>
          <button 
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="textbookName">教材名称 *</Label>
            <Input
              id="textbookName"
              name="textbookName"
              value={formData.textbookName}
              onChange={handleChange}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="isbn">ISBN</Label>
            <Input
              id="isbn"
              name="isbn"
              value={formData.isbn}
              onChange={handleChange}
            />
          </div>
          
          <div>
            <Label htmlFor="zlibId">Z-Library ID</Label>
            <Input
              id="zlibId"
              name="zlibId"
              value={formData.zlibId}
              onChange={handleChange}
            />
          </div>
          
          <div>
            <Label htmlFor="annaArchiveId">Anna's Archive ID</Label>
            <Input
              id="annaArchiveId"
              name="annaArchiveId"
              value={formData.annaArchiveId}
              onChange={handleChange}
            />
          </div>
          
          <div>
            <Label htmlFor="materialType">教材类型</Label>
            <select
              id="materialType"
              name="materialType"
              value={formData.materialType}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="TEXTBOOK">使用教材</option>
              <option value="REFERENCE">参考教材</option>
            </select>
          </div>
          
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '添加中...' : '添加'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}