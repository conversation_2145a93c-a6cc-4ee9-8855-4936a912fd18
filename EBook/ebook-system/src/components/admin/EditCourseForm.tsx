'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { adminApi } from '@/lib/api'
import { Course, Major, Textbook } from '@/types'


interface EditCourseFormProps {
  course: Course
  majors: Major[]
  textbooks: Textbook[]
  onSave: (updatedCourse: Course) => void
  onCancel: () => void
  onTextbooksUpdate: (textbooks: Textbook[]) => void
}

export default function EditCourseForm({ course, majors, textbooks, onSave, onCancel, onTextbooksUpdate }: EditCourseFormProps) {
  const [formData, setFormData] = useState({
    courseName: course.courseName,
    majorId: course.majorId?.toString() || '',
    referenceMaterials: course.referenceMaterials || ''
  })
  const [courseTextbooks, setCourseTextbooks] = useState<Textbook[]>(textbooks)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState('')
  const [showAddTextbook, setShowAddTextbook] = useState(false)
  const [addTextbookData, setAddTextbookData] = useState({
    textbookName: '',
    isbn: '',
    zlibId: '',
    annaArchiveId: '',
    materialType: 'TEXTBOOK'
  })
  const [addTextbookLoading, setAddTextbookLoading] = useState(false)
  const [addTextbookError, setAddTextbookError] = useState('')

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError('')

    try {
      const requestData: any = {
        ...formData,
        majorId: formData.majorId ? parseInt(formData.majorId) : null
      }
      
      const response = await adminApi.courses.update(course.id, requestData)
      
      if (response.data.success) {
        onSave({
          ...course,
          ...formData,
          majorId: formData.majorId ? parseInt(formData.majorId) : undefined
        })
      } else {
        setError(response.data.message || '更新失败')
      }
    } catch (err) {
      console.error('更新课程失败:', err)
      setError('更新课程时发生错误')
    } finally {
      setLoading(false)
    }
  }

  const handleAddTextbookChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target
    setAddTextbookData(prev => ({ ...prev, [name]: value }))
  }

  const handleAddTextbookSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setAddTextbookLoading(true)
    setAddTextbookError('')

    try {
      const response = await adminApi.textbooks.create({
        ...addTextbookData,
        courseId: course.id
      })

      if (response.data.success) {
        const newTextbook = response.data.data
        setCourseTextbooks(prev => [...prev, newTextbook])
        onTextbooksUpdate([...courseTextbooks, newTextbook])
        setAddTextbookData({
          textbookName: '',
          isbn: '',
          zlibId: '',
          annaArchiveId: '',
          materialType: 'TEXTBOOK'
        })
        setShowAddTextbook(false)
      } else {
        setAddTextbookError(response.data.message || '添加失败')
      }
    } catch (err) {
      console.error('添加教材失败:', err)
      setAddTextbookError('添加教材时发生错误')
    } finally {
      setAddTextbookLoading(false)
    }
  }

  const handleDeleteTextbook = async (id: number) => {
    if (confirm('确定要删除这本教材吗？')) {
      try {
        const response = await adminApi.textbooks.delete(id)
        if (response.data.success) {
          const updatedTextbooks = courseTextbooks.filter(tb => tb.id !== id)
          setCourseTextbooks(updatedTextbooks)
          onTextbooksUpdate(updatedTextbooks)
        } else {
          alert('删除失败: ' + response.data.message)
        }
      } catch (error) {
        console.error('删除教材失败:', error)
        alert('删除教材时发生错误')
      }
    }
  }

  return (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-2xl shadow-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">编辑课程</h3>
          <button 
            onClick={onCancel}
            className="text-gray-500 hover:text-gray-700"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        {error && (
          <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md text-sm">
            {error}
          </div>
        )}
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="courseName">课程名称 *</Label>
            <Input
              id="courseName"
              name="courseName"
              value={formData.courseName}
              onChange={handleChange}
              required
            />
          </div>
          
          <div>
            <Label htmlFor="majorId">所属专业</Label>
            <select
              id="majorId"
              name="majorId"
              value={formData.majorId}
              onChange={handleChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">未关联专业</option>
              {majors.map(major => (
                <option key={major.id} value={major.id.toString()}>
                  {major.majorName}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <Label htmlFor="referenceMaterials">参考资料</Label>
            <Textarea
              id="referenceMaterials"
              name="referenceMaterials"
              value={formData.referenceMaterials}
              onChange={handleChange}
              rows={3}
            />
          </div>
          
          <div>
            <div className="flex justify-between items-center mb-2">
              <Label>教材列表</Label>
              <Button type="button" onClick={() => setShowAddTextbook(true)} size="sm">
                添加教材
              </Button>
            </div>
            
            {courseTextbooks.length > 0 ? (
              <div className="border rounded-md">
                <table className="w-full">
                  <thead>
                    <tr className="bg-gray-50 border-b">
                      <th className="text-left py-2 px-3 text-sm font-medium">教材名称</th>
                      <th className="text-left py-2 px-3 text-sm font-medium">ISBN</th>
                      <th className="text-left py-2 px-3 text-sm font-medium">类型</th>
                      <th className="text-right py-2 px-3 text-sm font-medium">操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    {courseTextbooks.map((textbook) => (
                      <tr key={textbook.id} className="border-b hover:bg-gray-50">
                        <td className="py-2 px-3 text-sm max-w-xs truncate">
                          {textbook.textbookName}
                        </td>
                        <td className="py-2 px-3 text-sm">
                          {textbook.isbn || '-'}
                        </td>
                        <td className="py-2 px-3 text-sm">
                          <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                            textbook.materialType === 'TEXTBOOK' 
                              ? 'bg-blue-100 text-blue-800'
                              : textbook.materialType === 'REFERENCE'
                              ? 'bg-green-100 text-green-800'
                              : 'bg-gray-100 text-gray-800'
                          }`}>
                            {textbook.materialType === 'TEXTBOOK' ? '使用教材' : 
                             textbook.materialType === 'REFERENCE' ? '参考教材' : '未设置'}
                          </span>
                        </td>
                        <td className="py-2 px-3 text-right">
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteTextbook(textbook.id)}
                            className="text-red-600 hover:text-red-700 hover:border-red-300"
                          >
                            删除
                          </Button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 border rounded-md">
                暂无教材
              </div>
            )}

            {showAddTextbook && (
              <div className="mt-4 p-4 border rounded-md bg-gray-50">
                <div className="flex justify-between items-center mb-3">
                  <h4 className="text-sm font-medium">添加新教材</h4>
                  <button
                    type="button"
                    onClick={() => setShowAddTextbook(false)}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </button>
                </div>

                {addTextbookError && (
                  <div className="mb-3 p-2 bg-red-50 text-red-700 rounded-md text-sm">
                    {addTextbookError}
                  </div>
                )}

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="addTextbookName" className="text-sm">教材名称 *</Label>
                    <Input
                      id="addTextbookName"
                      name="textbookName"
                      value={addTextbookData.textbookName}
                      onChange={handleAddTextbookChange}
                      required
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="addIsbn" className="text-sm">ISBN</Label>
                    <Input
                      id="addIsbn"
                      name="isbn"
                      value={addTextbookData.isbn}
                      onChange={handleAddTextbookChange}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="addZlibId" className="text-sm">Z-Library ID</Label>
                    <Input
                      id="addZlibId"
                      name="zlibId"
                      value={addTextbookData.zlibId}
                      onChange={handleAddTextbookChange}
                      className="mt-1"
                    />
                  </div>

                  <div>
                    <Label htmlFor="addAnnaArchiveId" className="text-sm">Anna's Archive ID</Label>
                    <Input
                      id="addAnnaArchiveId"
                      name="annaArchiveId"
                      value={addTextbookData.annaArchiveId}
                      onChange={handleAddTextbookChange}
                      className="mt-1"
                    />
                  </div>

                  <div className="col-span-2">
                    <Label htmlFor="addMaterialType" className="text-sm">教材类型</Label>
                    <select
                      id="addMaterialType"
                      name="materialType"
                      value={addTextbookData.materialType}
                      onChange={handleAddTextbookChange}
                      className="mt-1 w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="TEXTBOOK">使用教材</option>
                      <option value="REFERENCE">参考教材</option>
                    </select>
                  </div>
                </div>

                <div className="flex justify-end space-x-2 mt-3">
                  <Button
                    type="button"
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAddTextbook(false)}
                    disabled={addTextbookLoading}
                  >
                    取消
                  </Button>
                  <Button
                    type="button"
                    size="sm"
                    onClick={handleAddTextbookSubmit}
                    disabled={addTextbookLoading}
                  >
                    {addTextbookLoading ? '添加中...' : '添加教材'}
                  </Button>
                </div>
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              取消
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? '保存中...' : '保存'}
            </Button>
          </div>
        </form>
      </div>

    </div>
  )
}