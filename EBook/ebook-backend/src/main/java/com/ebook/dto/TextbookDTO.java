package com.ebook.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 教材数据传输对象
 */
@Data
public class TextbookDTO {
    private Long id;
    private Long courseId;
    private String textbookName;
    private String isbn; // ISBN编号
    private String zlibId; // Z-Library ID
    private String annaArchiveId; // <PERSON>'s Archive ID
    private String materialType; // TEXTBOOK-使用教材，REFERENCE-参考教材
    private LocalDateTime createTime;
    private LocalDateTime updateTime;

    /**
     * 课程名称（关联字段）
     */
    private String courseName;
}
