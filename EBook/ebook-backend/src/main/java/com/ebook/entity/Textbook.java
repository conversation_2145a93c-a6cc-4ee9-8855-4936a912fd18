package com.ebook.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 教材实体类
 */
@Data
@TableName("textbooks")
@EqualsAndHashCode(callSuper = false)
public class Textbook {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("course_id")
    private Long courseId;

    @TableField("textbook_name")
    private String textbookName; // 完整引用信息

    @TableField("isbn")
    private String isbn; // ISBN编号

    @TableField("zlib_id")
    private String zlibId; // Z-Library ID

    @TableField("anna_archive_id")
    private String annaArchiveId; // Anna's Archive ID

    @TableField("material_type")
    private String materialType = "TEXTBOOK"; // TEXTBOOK-使用教材，REFERENCE-参考教材

    @TableField("created_by")
    private Long createdBy;

    @TableField("updated_by")
    private Long updatedBy;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;
}
