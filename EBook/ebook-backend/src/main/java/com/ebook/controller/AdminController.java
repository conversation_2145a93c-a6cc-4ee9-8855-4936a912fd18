package com.ebook.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ebook.dto.*;
import com.ebook.entity.*;
import com.ebook.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 管理后台控制器
 * 提供学校、学院、专业、课程、教材的完整CRUD操作
 */
@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*")
public class AdminController {

    @Autowired
    private SchoolService schoolService;
    
    @Autowired
    private CollegeService collegeService;
    
    @Autowired
    private MajorService majorService;
    
    @Autowired
    private CourseService courseService;
    
    @Autowired
    private TextbookService textbookService;

    // ==================== 仪表板统计 ====================
    
    /**
     * 获取管理后台仪表板统计数据
     */
    @GetMapping("/dashboard/stats")
    public ApiResponse<Map<String, Object>> getDashboardStats() {
        try {
            Map<String, Object> stats = new HashMap<>();
            
            // 统计各类数据数量
            stats.put("schoolCount", schoolService.count());
            stats.put("collegeCount", collegeService.count());
            stats.put("majorCount", majorService.count());
            stats.put("courseCount", courseService.count());
            stats.put("textbookCount", textbookService.count());
            
            // 系统状态信息
            Map<String, Object> systemInfo = new HashMap<>();
            systemInfo.put("status", "正常运行");
            systemInfo.put("dbConnection", "已连接");
            systemInfo.put("cacheStatus", "正常");
            systemInfo.put("lastUpdate", LocalDateTime.now());
            stats.put("systemInfo", systemInfo);
            
            return ApiResponse.success(stats);
        } catch (Exception e) {
            return ApiResponse.error("获取统计数据失败: " + e.getMessage());
        }
    }

    // ==================== 学校管理 ====================
    
    /**
     * 获取学校列表（分页）
     */
    @GetMapping("/schools")
    public ApiResponse<IPage<SchoolDTO>> getSchools(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword) {
        try {
            Page<School> pageParam = new Page<>(page, size);
            QueryWrapper<School> queryWrapper = new QueryWrapper<>();
            
            if (StringUtils.hasText(keyword)) {
                queryWrapper.like("school_name", keyword);
            }
            
            queryWrapper.orderByDesc("update_time");
            IPage<School> schoolPage = schoolService.page(pageParam, queryWrapper);
            
            // 转换为DTO并添加学院数量统计
            IPage<SchoolDTO> result = schoolPage.convert(school -> {
                SchoolDTO dto = new SchoolDTO();
                BeanUtils.copyProperties(school, dto);
                
                // 统计该学校的学院数量
                QueryWrapper<College> collegeQuery = new QueryWrapper<>();
                collegeQuery.eq("school_id", school.getId());
                dto.setCollegeCount((int) collegeService.count(collegeQuery));
                
                return dto;
            });
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("获取学校列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据ID获取学校详情
     */
    @GetMapping("/schools/{id}")
    public ApiResponse<SchoolDTO> getSchoolById(@PathVariable Long id) {
        try {
            School school = schoolService.getById(id);
            if (school == null) {
                return ApiResponse.error("学校不存在");
            }

            SchoolDTO dto = new SchoolDTO();
            BeanUtils.copyProperties(school, dto);

            // 添加学院数量统计
            QueryWrapper<College> collegeQuery = new QueryWrapper<>();
            collegeQuery.eq("school_id", school.getId());
            dto.setCollegeCount((int) collegeService.count(collegeQuery));

            return ApiResponse.success(dto);
        } catch (Exception e) {
            return ApiResponse.error("获取学校详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取学校名称（轻量级接口）
     */
    @GetMapping("/schools/{id}/name")
    public ApiResponse<String> getSchoolName(@PathVariable Long id) {
        try {
            School school = schoolService.getById(id);
            if (school == null) {
                return ApiResponse.error("学校不存在");
            }
            return ApiResponse.success(school.getSchoolName());
        } catch (Exception e) {
            return ApiResponse.error("获取学校名称失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建学校
     */
    @PostMapping("/schools")
    public ApiResponse<String> createSchool(@RequestBody SchoolDTO schoolDTO) {
        try {
            // 检查学校名称是否已存在
            QueryWrapper<School> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("school_name", schoolDTO.getSchoolName());
            if (schoolService.count(queryWrapper) > 0) {
                return ApiResponse.error("学校名称已存在");
            }
            
            School school = new School();
            BeanUtils.copyProperties(schoolDTO, school);
            school.setCreateTime(LocalDateTime.now());
            school.setUpdateTime(LocalDateTime.now());
            
            boolean success = schoolService.save(school);
            return success ? ApiResponse.success("创建学校成功") : ApiResponse.error("创建学校失败");
        } catch (Exception e) {
            return ApiResponse.error("创建学校失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新学校
     */
    @PutMapping("/schools/{id}")
    public ApiResponse<String> updateSchool(@PathVariable Long id, @RequestBody SchoolDTO schoolDTO) {
        try {
            School existingSchool = schoolService.getById(id);
            if (existingSchool == null) {
                return ApiResponse.error("学校不存在");
            }
            
            // 检查学校名称是否与其他学校重复
            QueryWrapper<School> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("school_name", schoolDTO.getSchoolName())
                       .ne("id", id);
            if (schoolService.count(queryWrapper) > 0) {
                return ApiResponse.error("学校名称已存在");
            }
            
            BeanUtils.copyProperties(schoolDTO, existingSchool, "id", "createTime");
            existingSchool.setUpdateTime(LocalDateTime.now());
            
            boolean success = schoolService.updateById(existingSchool);
            return success ? ApiResponse.success("更新学校成功") : ApiResponse.error("更新学校失败");
        } catch (Exception e) {
            return ApiResponse.error("更新学校失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除学校
     */
    @DeleteMapping("/schools/{id}")
    public ApiResponse<String> deleteSchool(@PathVariable Long id) {
        try {
            // 检查是否有关联的学院
            QueryWrapper<College> collegeQuery = new QueryWrapper<>();
            collegeQuery.eq("school_id", id);
            if (collegeService.count(collegeQuery) > 0) {
                return ApiResponse.error("该学校下还有学院，无法删除");
            }
            
            boolean success = schoolService.removeById(id);
            return success ? ApiResponse.success("删除学校成功") : ApiResponse.error("删除学校失败");
        } catch (Exception e) {
            return ApiResponse.error("删除学校失败: " + e.getMessage());
        }
    }

    // ==================== 学院管理 ====================
    
    /**
     * 获取学院列表（分页）
     */
    @GetMapping("/colleges")
    public ApiResponse<IPage<CollegeDTO>> getColleges(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long schoolId) {
        try {
            Page<College> pageParam = new Page<>(page, size);
            QueryWrapper<College> queryWrapper = new QueryWrapper<>();

            if (StringUtils.hasText(keyword)) {
                queryWrapper.like("college_name", keyword);
            }

            if (schoolId != null) {
                queryWrapper.eq("school_id", schoolId);
            }

            queryWrapper.orderByDesc("update_time");
            IPage<College> collegePage = collegeService.page(pageParam, queryWrapper);

            // 转换为DTO并添加关联信息
            IPage<CollegeDTO> result = collegePage.convert(college -> {
                CollegeDTO dto = new CollegeDTO();
                // 复制基础字段
                dto.setId(college.getId());
                dto.setSchoolId(college.getSchoolId());
                dto.setCollegeName(college.getCollegeName());
                dto.setCreateTime(college.getCreateTime());
                dto.setUpdateTime(college.getUpdateTime());

                // 获取学校名称 - 如果没有学校数据，直接返回默认值
                if (college.getSchoolId() != null) {
                    try {
                        School school = schoolService.getById(college.getSchoolId());
                        if (school != null) {
                            dto.setSchoolName(school.getSchoolName());
                        } else {
                            // 如果学校不存在，根据ID返回默认名称
                            dto.setSchoolName("学校ID:" + college.getSchoolId());
                        }
                    } catch (Exception e) {
                        dto.setSchoolName("学校ID:" + college.getSchoolId());
                    }
                } else {
                    dto.setSchoolName("未设置学校");
                }

                // 统计专业数量
                try {
                    long majorCount = majorService.getMajorCountByCollegeId(college.getId());
                    dto.setMajorCount((int) majorCount);
                } catch (Exception e) {
                    dto.setMajorCount(0);
                }

                return dto;
            });

            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("获取学院列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建学院
     */
    @PostMapping("/colleges")
    public ApiResponse<String> createCollege(@RequestBody CollegeDTO collegeDTO) {
        try {
            // 验证学校是否存在
            if (schoolService.getById(collegeDTO.getSchoolId()) == null) {
                return ApiResponse.error("指定的学校不存在");
            }
            
            // 检查同一学校下学院名称是否重复
            QueryWrapper<College> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("school_id", collegeDTO.getSchoolId())
                       .eq("college_name", collegeDTO.getCollegeName());
            if (collegeService.count(queryWrapper) > 0) {
                return ApiResponse.error("该学校下已存在同名学院");
            }
            
            College college = new College();
            BeanUtils.copyProperties(collegeDTO, college);
            college.setCreateTime(LocalDateTime.now());
            college.setUpdateTime(LocalDateTime.now());
            
            boolean success = collegeService.save(college);
            return success ? ApiResponse.success("创建学院成功") : ApiResponse.error("创建学院失败");
        } catch (Exception e) {
            return ApiResponse.error("创建学院失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新学院
     */
    @PutMapping("/colleges/{id}")
    public ApiResponse<String> updateCollege(@PathVariable Long id, @RequestBody CollegeDTO collegeDTO) {
        try {
            College existingCollege = collegeService.getById(id);
            if (existingCollege == null) {
                return ApiResponse.error("学院不存在");
            }
            
            // 验证学校是否存在
            if (schoolService.getById(collegeDTO.getSchoolId()) == null) {
                return ApiResponse.error("指定的学校不存在");
            }
            
            // 检查同一学校下学院名称是否重复
            QueryWrapper<College> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("school_id", collegeDTO.getSchoolId())
                       .eq("college_name", collegeDTO.getCollegeName())
                       .ne("id", id);
            if (collegeService.count(queryWrapper) > 0) {
                return ApiResponse.error("该学校下已存在同名学院");
            }
            
            BeanUtils.copyProperties(collegeDTO, existingCollege, "id", "createTime");
            existingCollege.setUpdateTime(LocalDateTime.now());
            
            boolean success = collegeService.updateById(existingCollege);
            return success ? ApiResponse.success("更新学院成功") : ApiResponse.error("更新学院失败");
        } catch (Exception e) {
            return ApiResponse.error("更新学院失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除学院
     */
    @DeleteMapping("/colleges/{id}")
    public ApiResponse<String> deleteCollege(@PathVariable Long id) {
        try {
            // 检查是否有关联的专业
            QueryWrapper<Major> majorQuery = new QueryWrapper<>();
            majorQuery.eq("college_id", id);
            if (majorService.count(majorQuery) > 0) {
                return ApiResponse.error("该学院下还有专业，无法删除");
            }
            
            boolean success = collegeService.removeById(id);
            return success ? ApiResponse.success("删除学院成功") : ApiResponse.error("删除学院失败");
        } catch (Exception e) {
            return ApiResponse.error("删除学院失败: " + e.getMessage());
        }
    }

    // ==================== 专业管理 ====================
    
    /**
     * 获取专业列表（分页）
     */
    @GetMapping("/majors")
    public ApiResponse<IPage<MajorDTO>> getMajors(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long collegeId,
            @RequestParam(required = false) Long schoolId) {
        try {
            Page<Major> pageParam = new Page<>(page, size);
            QueryWrapper<Major> queryWrapper = new QueryWrapper<>();
            
            if (StringUtils.hasText(keyword)) {
                queryWrapper.and(wrapper -> wrapper
                    .like("major_name", keyword));
            }
            
            if (collegeId != null) {
                queryWrapper.eq("college_id", collegeId);
            } else if (schoolId != null) {
                // 如果指定了学校但没有指定学院，查询该学校下所有学院的专业
                QueryWrapper<College> collegeQuery = new QueryWrapper<>();
                collegeQuery.eq("school_id", schoolId);
                List<College> colleges = collegeService.list(collegeQuery);
                if (!colleges.isEmpty()) {
                    List<Long> collegeIds = colleges.stream().map(College::getId).toList();
                    queryWrapper.in("college_id", collegeIds);
                }
            }
            
            queryWrapper.orderByDesc("update_time");
            IPage<Major> majorPage = majorService.page(pageParam, queryWrapper);
            
            // 转换为DTO并添加关联信息
            IPage<MajorDTO> result = majorPage.convert(major -> {
                MajorDTO dto = new MajorDTO();
                BeanUtils.copyProperties(major, dto);

                // 获取学院名称和学校名称
                College college = collegeService.getById(major.getCollegeId());
                if (college != null) {
                    dto.setCollegeName(college.getCollegeName());

                    // 获取学校名称
                    School school = schoolService.getById(college.getSchoolId());
                    if (school != null) {
                        dto.setSchoolName(school.getSchoolName());
                    } else {
                        dto.setSchoolName("未知学校");
                    }
                } else {
                    dto.setCollegeName("未知学院");
                    dto.setSchoolName("未知学校");
                }

                // 统计课程数量
                QueryWrapper<Course> courseQuery = new QueryWrapper<>();
                courseQuery.eq("major_id", major.getId());
                dto.setCourseCount((int) courseService.count(courseQuery));

                return dto;
            });
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("获取专业列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建专业
     */
    @PostMapping("/majors")
    public ApiResponse<String> createMajor(@RequestBody MajorDTO majorDTO) {
        try {
            // 验证学院是否存在
            if (collegeService.getById(majorDTO.getCollegeId()) == null) {
                return ApiResponse.error("指定的学院不存在");
            }
            
            // 检查同一学院下专业名称是否重复
            QueryWrapper<Major> nameQuery = new QueryWrapper<>();
            nameQuery.eq("college_id", majorDTO.getCollegeId())
                    .eq("major_name", majorDTO.getMajorName());
            if (majorService.count(nameQuery) > 0) {
                return ApiResponse.error("该学院下已存在同名专业");
            }
            
            Major major = new Major();
            BeanUtils.copyProperties(majorDTO, major);
            major.setCreateTime(LocalDateTime.now());
            major.setUpdateTime(LocalDateTime.now());
            
            boolean success = majorService.save(major);
            return success ? ApiResponse.success("创建专业成功") : ApiResponse.error("创建专业失败");
        } catch (Exception e) {
            return ApiResponse.error("创建专业失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新专业
     */
    @PutMapping("/majors/{id}")
    public ApiResponse<String> updateMajor(@PathVariable Long id, @RequestBody MajorDTO majorDTO) {
        try {
            Major existingMajor = majorService.getById(id);
            if (existingMajor == null) {
                return ApiResponse.error("专业不存在");
            }
            
            // 验证学院是否存在
            if (collegeService.getById(majorDTO.getCollegeId()) == null) {
                return ApiResponse.error("指定的学院不存在");
            }
            
            // 检查同一学院下专业名称是否重复
            QueryWrapper<Major> nameQuery = new QueryWrapper<>();
            nameQuery.eq("college_id", majorDTO.getCollegeId())
                    .eq("major_name", majorDTO.getMajorName())
                    .ne("id", id);
            if (majorService.count(nameQuery) > 0) {
                return ApiResponse.error("该学院下已存在同名专业");
            }
            
            BeanUtils.copyProperties(majorDTO, existingMajor, "id", "createTime");
            existingMajor.setUpdateTime(LocalDateTime.now());
            
            boolean success = majorService.updateById(existingMajor);
            return success ? ApiResponse.success("更新专业成功") : ApiResponse.error("更新专业失败");
        } catch (Exception e) {
            return ApiResponse.error("更新专业失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除专业
     */
    @DeleteMapping("/majors/{id}")
    public ApiResponse<String> deleteMajor(@PathVariable Long id) {
        try {
            // 检查是否有关联的课程
            QueryWrapper<Course> courseQuery = new QueryWrapper<>();
            courseQuery.eq("major_id", id);
            if (courseService.count(courseQuery) > 0) {
                return ApiResponse.error("该专业下还有课程，无法删除");
            }
            
            boolean success = majorService.removeById(id);
            return success ? ApiResponse.success("删除专业成功") : ApiResponse.error("删除专业失败");
        } catch (Exception e) {
            return ApiResponse.error("删除专业失败: " + e.getMessage());
        }
    }

    // ==================== 课程管理 ====================
    
    /**
     * 获取课程列表（分页）
     */
    @GetMapping("/courses")
    public ApiResponse<IPage<CourseDTO>> getCourses(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long majorId) {
        try {
            Page<Course> pageParam = new Page<>(page, size);
            QueryWrapper<Course> queryWrapper = new QueryWrapper<>();
            
            if (StringUtils.hasText(keyword)) {
                queryWrapper.and(wrapper -> wrapper
                    .like("course_name", keyword));
            }
            
            if (majorId != null) {
                queryWrapper.eq("major_id", majorId);
            }
            
            queryWrapper.orderByDesc("update_time");
            IPage<Course> coursePage = courseService.page(pageParam, queryWrapper);
            
            // 转换为DTO并添加关联信息
            IPage<CourseDTO> result = coursePage.convert(course -> {
                CourseDTO dto = new CourseDTO();
                BeanUtils.copyProperties(course, dto);
                
                // 获取专业名称
                if (course.getMajorId() != null) {
                    Major major = majorService.getById(course.getMajorId());
                    if (major != null) {
                        dto.setMajorName(major.getMajorName());
                    }
                }
                
                // 统计教材数量
                QueryWrapper<Textbook> textbookQuery = new QueryWrapper<>();
                textbookQuery.eq("course_id", course.getId());
                dto.setTextbookCount((int) textbookService.count(textbookQuery));
                
                return dto;
            });
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("获取课程列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建课程
     */
    @PostMapping("/courses")
    public ApiResponse<String> createCourse(@RequestBody CourseDTO courseDTO) {
        try {
            // 验证专业是否存在（如果指定了专业）
            if (courseDTO.getMajorId() != null && majorService.getById(courseDTO.getMajorId()) == null) {
                return ApiResponse.error("指定的专业不存在");
            }
            
            Course course = new Course();
            BeanUtils.copyProperties(courseDTO, course);
            course.setCreateTime(LocalDateTime.now());
            course.setUpdateTime(LocalDateTime.now());
            
            boolean success = courseService.save(course);
            return success ? ApiResponse.success("创建课程成功") : ApiResponse.error("创建课程失败");
        } catch (Exception e) {
            return ApiResponse.error("创建课程失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新课程
     */
    @PutMapping("/courses/{id}")
    public ApiResponse<String> updateCourse(@PathVariable Long id, @RequestBody CourseDTO courseDTO) {
        try {
            Course existingCourse = courseService.getById(id);
            if (existingCourse == null) {
                return ApiResponse.error("课程不存在");
            }
            
            // 验证专业是否存在（如果指定了专业）
            if (courseDTO.getMajorId() != null && majorService.getById(courseDTO.getMajorId()) == null) {
                return ApiResponse.error("指定的专业不存在");
            }
            
            BeanUtils.copyProperties(courseDTO, existingCourse, "id", "createTime");
            existingCourse.setUpdateTime(LocalDateTime.now());
            
            boolean success = courseService.updateById(existingCourse);
            return success ? ApiResponse.success("更新课程成功") : ApiResponse.error("更新课程失败");
        } catch (Exception e) {
            return ApiResponse.error("更新课程失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除课程
     */
    @DeleteMapping("/courses/{id}")
    public ApiResponse<String> deleteCourse(@PathVariable Long id) {
        try {
            // 检查是否有关联的教材
            QueryWrapper<Textbook> textbookQuery = new QueryWrapper<>();
            textbookQuery.eq("course_id", id);
            if (textbookService.count(textbookQuery) > 0) {
                return ApiResponse.error("该课程下还有教材，无法删除");
            }
            
            boolean success = courseService.removeById(id);
            return success ? ApiResponse.success("删除课程成功") : ApiResponse.error("删除课程失败");
        } catch (Exception e) {
            return ApiResponse.error("删除课程失败: " + e.getMessage());
        }
    }

    // ==================== 教材管理 ====================
    
    /**
     * 获取教材列表（分页）
     */
    @GetMapping("/textbooks")
    public ApiResponse<IPage<TextbookDTO>> getTextbooks(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String materialType,
            @RequestParam(required = false) Long courseId) {
        try {
            Page<Textbook> pageParam = new Page<>(page, size);
            QueryWrapper<Textbook> queryWrapper = new QueryWrapper<>();
            
            if (StringUtils.hasText(keyword)) {
                queryWrapper.and(wrapper -> wrapper
                    .like("textbook_name", keyword));
            }
            
            if (StringUtils.hasText(materialType)) {
                queryWrapper.eq("material_type", materialType);
            }
            
            if (courseId != null) {
                queryWrapper.eq("course_id", courseId);
            }
            
            queryWrapper.orderByDesc("update_time");
            IPage<Textbook> textbookPage = textbookService.page(pageParam, queryWrapper);
            
            // 转换为DTO并添加关联信息
            IPage<TextbookDTO> result = textbookPage.convert(textbook -> {
                TextbookDTO dto = new TextbookDTO();
                BeanUtils.copyProperties(textbook, dto);
                
                // 获取关联的课程名称
                if (textbook.getCourseId() != null) {
                    Course course = courseService.getById(textbook.getCourseId());
                    if (course != null) {
                        dto.setCourseName(course.getCourseName());
                    }
                }
                
                return dto;
            });
            
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error("获取教材列表失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建教材
     */
    @PostMapping("/textbooks")
    public ApiResponse<Textbook> createTextbook(@RequestBody TextbookDTO textbookDTO) {
        try {
            // 验证课程是否存在（如果指定了课程）
            if (textbookDTO.getCourseId() != null && courseService.getById(textbookDTO.getCourseId()) == null) {
                return ApiResponse.error("指定的课程不存在");
            }

            Textbook textbook = new Textbook();
            BeanUtils.copyProperties(textbookDTO, textbook);
            textbook.setCreateTime(LocalDateTime.now());
            textbook.setUpdateTime(LocalDateTime.now());

            boolean success = textbookService.save(textbook);
            if (success) {
                return ApiResponse.success(textbook);
            } else {
                return ApiResponse.error("创建教材失败");
            }
        } catch (Exception e) {
            return ApiResponse.error("创建教材失败: " + e.getMessage());
        }
    }
    
    /**
     * 更新教材
     */
    @PutMapping("/textbooks/{id}")
    public ApiResponse<String> updateTextbook(@PathVariable Long id, @RequestBody TextbookDTO textbookDTO) {
        try {
            Textbook existingTextbook = textbookService.getById(id);
            if (existingTextbook == null) {
                return ApiResponse.error("教材不存在");
            }
            
            // 验证课程是否存在（如果指定了课程）
            if (textbookDTO.getCourseId() != null && courseService.getById(textbookDTO.getCourseId()) == null) {
                return ApiResponse.error("指定的课程不存在");
            }
            
            BeanUtils.copyProperties(textbookDTO, existingTextbook, "id", "createTime");
            existingTextbook.setUpdateTime(LocalDateTime.now());
            
            boolean success = textbookService.updateById(existingTextbook);
            return success ? ApiResponse.success("更新教材成功") : ApiResponse.error("更新教材失败");
        } catch (Exception e) {
            return ApiResponse.error("更新教材失败: " + e.getMessage());
        }
    }
    
    /**
     * 删除教材
     */
    @DeleteMapping("/textbooks/{id}")
    public ApiResponse<String> deleteTextbook(@PathVariable Long id) {
        try {
            boolean success = textbookService.removeById(id);
            return success ? ApiResponse.success("删除教材成功") : ApiResponse.error("删除教材失败");
        } catch (Exception e) {
            return ApiResponse.error("删除教材失败: " + e.getMessage());
        }
    }
}