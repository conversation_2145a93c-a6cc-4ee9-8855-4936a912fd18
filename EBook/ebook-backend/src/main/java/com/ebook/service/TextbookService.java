package com.ebook.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ebook.dto.TextbookDTO;
import com.ebook.entity.Textbook;
import com.ebook.mapper.TextbookMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 教材服务类
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class TextbookService extends ServiceImpl<TextbookMapper, Textbook> {

    private final TextbookMapper textbookMapper;

    /**
     * 获取所有教材列表（分页）
     */
    public Page<TextbookDTO> getAllTextbooks(int page, int pageSize) {
        Page<Textbook> textbookPage = new Page<>(page, pageSize);
        Page<Textbook> result = textbookMapper.selectPage(textbookPage, null);

        Page<TextbookDTO> dtoPage = new Page<>(page, pageSize);
        dtoPage.setTotal(result.getTotal());
        dtoPage.setRecords(result.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList()));
        return dtoPage;
    }

    /**
     * 根据课程代码获取教材列表
     */
    public List<TextbookDTO> getTextbooksByCourseCode(String courseCode) {
        try {
            // 将courseCode转换为Long类型的courseId
            Long courseId = Long.valueOf(courseCode);
            
            QueryWrapper<Textbook> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("course_id", courseId);
            
            List<Textbook> textbooks = textbookMapper.selectList(queryWrapper);
            return textbooks.stream()
                    .map(this::convertToDTO)
                    .collect(Collectors.toList());
        } catch (NumberFormatException e) {
            log.warn("无效的课程代码: {}", courseCode);
            return List.of();
        }
    }

    /**
     * 根据关键词搜索教材
     */
    public Page<TextbookDTO> searchTextbooks(String keyword, int page, int pageSize) {
        if (keyword == null || keyword.trim().isEmpty()) {
            return getAllTextbooks(page, pageSize);
        }

        Page<Textbook> textbookPage = new Page<>(page, pageSize);
        QueryWrapper<Textbook> queryWrapper = new QueryWrapper<>();
        queryWrapper.like("textbook_name", keyword.trim());
        
        Page<Textbook> result = textbookMapper.selectPage(textbookPage, queryWrapper);

        Page<TextbookDTO> dtoPage = new Page<>(page, pageSize);
        dtoPage.setTotal(result.getTotal());
        dtoPage.setRecords(result.getRecords().stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList()));
        return dtoPage;
    }

    /**
     * 根据ID获取教材详情
     */
    public Optional<TextbookDTO> getTextbookById(Long id) {
        log.debug("根据ID获取教材详情: {}", id);
        Textbook textbook = textbookMapper.selectById(id);
        return Optional.ofNullable(textbook).map(this::convertToDTO);
    }

    /**
     * 转换为DTO
     */
    private TextbookDTO convertToDTO(Textbook textbook) {
        TextbookDTO dto = new TextbookDTO();
        dto.setId(textbook.getId());
        dto.setCourseId(textbook.getCourseId());
        dto.setTextbookName(textbook.getTextbookName());
        dto.setIsbn(textbook.getIsbn());
        dto.setZlibId(textbook.getZlibId());
        dto.setAnnaArchiveId(textbook.getAnnaArchiveId());
        dto.setMaterialType(textbook.getMaterialType());
        dto.setCreateTime(textbook.getCreateTime());
        dto.setUpdateTime(textbook.getUpdateTime());
        return dto;
    }
}
